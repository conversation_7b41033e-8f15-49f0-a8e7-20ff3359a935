{"current_level": 12, "score": 31200, "high_score": 31200, "player_data": {"health": 140, "max_health": 120, "damage": 25, "speed": 6.9, "fire_rate": 100, "level": 11, "xp": 4361, "xp_to_next_level": 5743, "upgrade_points": 0, "progression_data": {"skill_tree": {"skill_points": 0, "learned_skills": {"critical_strike": 1, "multi_shot": 3, "piercing_shots": 1, "explosive_shots": 2, "armor_mastery": 1, "health_regeneration": 1, "movement_mastery": 1}, "active_synergies": []}, "equipment_manager": {"equipped": {"weapon": {"equipment_type": "weapon", "name": "Destroyer", "rarity": "Uncommon", "level": 1, "stats": {"critical_chance": 0.79, "damage_bonus": 68}}, "armor": {"equipment_type": "armor", "name": "Aegis", "rarity": "Uncommon", "level": 1, "stats": {"damage_reduction": 0.92, "regeneration": 10.73}}, "accessory": {"equipment_type": "accessory", "name": "Charm", "rarity": "Common", "level": 1, "stats": {"item_find": 0.85, "resource_bonus": 1.31, "xp_bonus": 1.02}}}, "inventory": [{"equipment_type": "armor", "name": "Plate", "rarity": "Uncommon", "level": 1, "stats": {"health_bonus": 138, "regeneration": 8.99, "speed_bonus": 6}}, {"equipment_type": "armor", "name": "Fortress", "rarity": "Rare", "level": 1, "stats": {"health_bonus": 282, "damage_reduction": 1.35}}, {"equipment_type": "accessory", "name": "Charm", "rarity": "Uncommon", "level": 1, "stats": {"resource_bonus": 2.38}}, {"equipment_type": "armor", "name": "Vest", "rarity": "Uncommon", "level": 1, "stats": {"damage_reduction": 1.09, "speed_bonus": 8, "health_bonus": 230}}, {"equipment_type": "accessory", "name": "Amulet", "rarity": "Uncommon", "level": 1, "stats": {"resource_bonus": 1.97}}, {"equipment_type": "weapon", "name": "Rifle", "rarity": "Common", "level": 1, "stats": {"damage_bonus": 63}}, {"equipment_type": "weapon", "name": "Sword", "rarity": "Common", "level": 1, "stats": {"critical_chance": 0.53}}, {"equipment_type": "armor", "name": "Vest", "rarity": "Uncommon", "level": 1, "stats": {"health_bonus": 207, "regeneration": 14.1}}, {"equipment_type": "armor", "name": "Plate", "rarity": "Uncommon", "level": 1, "stats": {"damage_reduction": 1.29, "health_bonus": 252}}, {"equipment_type": "armor", "name": "Shield", "rarity": "Uncommon", "level": 1, "stats": {"damage_reduction": 1.18}}, {"equipment_type": "armor", "name": "Vest", "rarity": "Rare", "level": 1, "stats": {"health_bonus": 339, "regeneration": 20.58}}, {"equipment_type": "accessory", "name": "<PERSON><PERSON>", "rarity": "Common", "level": 1, "stats": {"item_find": 1.17, "resource_bonus": 1.63}}, {"equipment_type": "weapon", "name": "Annihilator", "rarity": "Common", "level": 1, "stats": {"critical_chance": 0.61}}, {"equipment_type": "accessory", "name": "Ring", "rarity": "Rare", "level": 1, "stats": {"skill_cooldown": 1.77}}, {"equipment_type": "accessory", "name": "Talisman", "rarity": "Uncommon", "level": 1, "stats": {"skill_cooldown": 1.16}}, {"equipment_type": "armor", "name": "Barrier", "rarity": "Uncommon", "level": 1, "stats": {"health_bonus": 274, "damage_reduction": 1.5}}, {"equipment_type": "weapon", "name": "Decimator", "rarity": "Common", "level": 1, "stats": {"critical_chance": 0.7, "projectile_speed": 21, "damage_bonus": 67}}, {"equipment_type": "accessory", "name": "Artifact", "rarity": "Common", "level": 1, "stats": {"item_find": 1.42, "skill_cooldown": 0.9, "xp_bonus": 1.57}}, {"equipment_type": "accessory", "name": "Charm", "rarity": "Rare", "level": 1, "stats": {"item_find": 2.58}}, {"equipment_type": "armor", "name": "Bulwark", "rarity": "Common", "level": 1, "stats": {"health_bonus": 209, "damage_reduction": 0.91}}]}, "achievement_manager": {"achievements": {"first_steps": {"name": "First Steps", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "experienced": {"name": "Experienced", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "veteran": {"name": "Veteran", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "master": {"name": "Master", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "first_blood": {"name": "First Blood", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "slayer": {"name": "Slayer", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "destroyer": {"name": "Destroyer", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "boss_hunter": {"name": "<PERSON>", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "boss_slayer": {"name": "Boss Slayer", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "survivor": {"name": "Survivor", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "untouchable": {"name": "Untouchable", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "iron_will": {"name": "Iron Will", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "skill_student": {"name": "Skill Student", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "skill_master": {"name": "Skill Master", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "well_equipped": {"name": "Well Equipped", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "fully_equipped": {"name": "Fully Equipped", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "collector": {"name": "Collector", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "hoarder": {"name": "Hoarder", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "speed_runner": {"name": "Speed Runner", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "perfectionist": {"name": "Perfectionist", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "secret_finder": {"name": "Secret Finder", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "lucky_shot": {"name": "Lucky Shot", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "enemy_slayer": {"name": "Enemy Slayer", "unlocked": true, "progress": 100, "achievement_type": "progressive", "max_progress": 100}, "damage_dealer": {"name": "Damage Dealer", "unlocked": false, "progress": 0, "achievement_type": "progressive", "max_progress": 10000}, "treasure_hunter": {"name": "Treasure Hunter", "unlocked": false, "progress": 23, "achievement_type": "progressive", "max_progress": 50}, "combat_master": {"name": "Combat Master", "unlocked": false, "progress": 0, "achievement_type": "chain", "max_progress": 1}, "immortal": {"name": "<PERSON><PERSON><PERSON><PERSON>", "unlocked": false, "progress": 0, "achievement_type": "chain", "max_progress": 1}, "master_explorer": {"name": "Master Explorer", "unlocked": false, "progress": 0, "achievement_type": "chain", "max_progress": 1}}, "completed_chains": []}, "stats": {"enemies_killed": 189, "bosses_killed": 0, "levels_completed": 0, "perfect_levels": 0, "near_death_survivals": 0, "skills_learned": 0, "maxed_skills": 0, "maxed_combat_skills": 0, "equipment_equipped": 23, "full_equipment_sets": 15, "items_collected": 23, "secrets_found": 0, "max_crit_streak": 34, "current_crit_streak": 8, "fastest_level_time": Infinity, "player_level": 11, "total_damage_dealt": 0}, "regen_timer": 25}}}